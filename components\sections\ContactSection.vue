<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >
    <div class="grid lg:grid-cols-2 gap-12">
      <!-- Contact Information -->
      <div class="space-y-8">
        <div class="space-y-6">
          <!-- Address -->
          <div v-if="address" class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:map-pin" class="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 class="font-semibold text-primary mb-1">Address</h3>
              <p class="text-primary/80">{{ address }}</p>
            </div>
          </div>
          
          <!-- Phone -->
          <div v-if="phone" class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:phone" class="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 class="font-semibold text-primary mb-1">Phone</h3>
              <a :href="`tel:${phone}`" class="text-primary/80 hover:text-primary transition-colors">
                {{ phone }}
              </a>
            </div>
          </div>
          
          <!-- Email -->
          <div v-if="email" class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:envelope" class="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 class="font-semibold text-primary mb-1">Email</h3>
              <a :href="`mailto:${email}`" class="text-primary/80 hover:text-primary transition-colors">
                {{ email }}
              </a>
            </div>
          </div>
          
          <!-- Hours -->
          <div v-if="hours && hours.length" class="flex items-start space-x-4">
            <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon name="heroicons:clock" class="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 class="font-semibold text-primary mb-2">Hours</h3>
              <div class="space-y-1">
                <div
                  v-for="(hour, index) in hours"
                  :key="index"
                  class="flex justify-between text-sm"
                >
                  <span class="text-primary/80">{{ hour.day }}</span>
                  <span class="text-primary/80">{{ hour.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Social Links -->
        <div v-if="socialLinks && socialLinks.length" class="pt-6 border-t border-primary/10">
          <h3 class="font-semibold text-primary mb-4">Follow Us</h3>
          <div class="flex space-x-4">
            <a
              v-for="(social, index) in socialLinks"
              :key="index"
              :href="social.url"
              :target="social.external ? '_blank' : '_self'"
              :rel="social.external ? 'noopener noreferrer' : ''"
              class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center hover:bg-primary hover:text-secondary transition-colors"
            >
              <Icon :name="social.icon" class="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>
      
      <!-- Map or Contact Form -->
      <div class="space-y-6">
        <!-- Map -->
        <div v-if="mapEmbedUrl" class="aspect-video rounded-xl overflow-hidden">
          <iframe
            :src="mapEmbedUrl"
            width="100%"
            height="100%"
            style="border:0;"
            allowfullscreen=""
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
        
        <!-- Contact Form -->
        <Card v-if="showContactForm" variant="elevated" size="lg">
          <template #title>
            <h3 class="text-xl font-bold text-primary">Send us a message</h3>
          </template>
          
          <template #content>
            <form @submit.prevent="handleFormSubmit" class="space-y-4">
              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-primary mb-2">Name</label>
                  <input
                    v-model="form.name"
                    type="text"
                    required
                    class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-primary mb-2">Phone</label>
                  <input
                    v-model="form.phone"
                    type="tel"
                    class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                  >
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-primary mb-2">Email</label>
                <input
                  v-model="form.email"
                  type="email"
                  required
                  class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-primary mb-2">Message</label>
                <textarea
                  v-model="form.message"
                  rows="4"
                  required
                  class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary resize-none"
                ></textarea>
              </div>
              
              <Button type="submit" variant="primary" size="lg" full-width>
                Send Message
              </Button>
            </form>
          </template>
        </Card>
      </div>
    </div>
  </SectionContainer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import SectionContainer from '../ui/SectionContainer.vue'

interface Hour {
  day: string
  time: string
}

interface SocialLink {
  icon: string
  url: string
  external?: boolean
}

interface Props {
  id?: string
  title?: string
  subtitle?: string
  address?: string
  phone?: string
  email?: string
  hours?: Hour[]
  socialLinks?: SocialLink[]
  mapEmbedUrl?: string
  showContactForm?: boolean
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  showContactForm: true
})

const emit = defineEmits<{
  formSubmit: [formData: any]
}>()

const form = ref({
  name: '',
  phone: '',
  email: '',
  message: ''
})

const handleFormSubmit = () => {
  emit('formSubmit', { ...form.value })
  // Reset form
  form.value = {
    name: '',
    phone: '',
    email: '',
    message: ''
  }
}
</script>
