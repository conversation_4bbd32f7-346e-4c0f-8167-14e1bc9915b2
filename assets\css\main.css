@import "tailwindcss";

@theme {
    /* Modern Professional Theme - Perfect for various businesses */
    --color-primary: #1e40af;      /* Deep Blue - Trust, professionalism */
    --color-secondary: #ffffff;    /* Pure White - Clean, modern */
    --color-tertiary: #f1f5f9;     /* Light Blue-Gray - Subtle backgrounds */
    --color-background: #f8fafc;   /* Off-White - Main background */

    /* Additional theme colors for better design system */
    --color-accent: #3b82f6;       /* Bright Blue - CTAs, highlights */
    --color-muted: #64748b;        /* Gray - Secondary text */
    --color-success: #10b981;      /* Green - Success states */
    --color-warning: #f59e0b;      /* Amber - Warnings */
    --color-error: #ef4444;        /* Red - Errors */
}

/* Global styles for consistent theming */
body {
    background-color: var(--color-background);
    color: var(--color-primary);
}

/* Smooth transitions for theme changes */
* {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Hide scrollbar completely */
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

/* For Firefox */
html {
    scrollbar-width: none;
}

/* For Internet Explorer and Edge */
body {
    -ms-overflow-style: none;
}