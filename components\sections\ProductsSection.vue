<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >
    <!-- Filter/Category Tabs -->
    <div v-if="categories && categories.length > 1" class="flex flex-wrap justify-center gap-2 mb-8">
      <Button
        v-for="category in categories"
        :key="category.slug"
        :variant="selectedCategory === category.slug ? 'primary' : 'outline'"
        size="sm"
        @click="selectCategory(category.slug)"
      >
        {{ category.name }}
      </Button>
    </div>
    
    <!-- Products Grid -->
    <div :class="gridClasses">
      <ProductCard
        v-for="product in filteredProducts"
        :key="product.id || product.slug"
        :name="product.name"
        :description="product.description"
        :image-url="product.image"
        :image-alt="product.imageAlt || product.name"
        :price="product.price"
        :original-price="product.originalPrice"
        :price-unit="product.priceUnit"
        :currency="product.currency || currency"
        :features="product.features"
        :badge="product.badge"
        :in-stock="product.inStock"
        :stock-count="product.stockCount"
        :show-stock="showStock"
        :rating="product.rating"
        :review-count="product.reviewCount"
        :primary-action="product.primaryAction || defaultPrimaryAction"
        :secondary-action="product.secondaryAction || defaultSecondaryAction"
        :show-quick-actions="showQuickActions"
        :compare-enabled="compareEnabled"
        :variant="cardVariant"
        :size="cardSize"
        :hover="true"
        :clickable="clickableCards"
        @click="handleProductClick(product)"
        @primary-action="handleProductAction(product, 'primary')"
        @secondary-action="handleProductAction(product, 'secondary')"
        @wishlist="handleWishlist(product)"
        @share="handleShare(product)"
        @compare="handleCompare(product)"
      />
    </div>
    
    <!-- Load More Button -->
    <div v-if="showLoadMore && hasMoreProducts" class="text-center mt-8">
      <Button
        variant="outline"
        size="lg"
        icon="heroicons:arrow-down"
        :loading="loadingMore"
        @click="loadMore"
      >
        Load More Products
      </Button>
    </div>
    
    <!-- Bottom CTA -->
    <div v-if="bottomCta" class="text-center mt-12">
      <Button
        :variant="bottomCta.variant || 'primary'"
        :size="bottomCta.size || 'lg'"
        :icon="bottomCta.icon"
        :href="bottomCta.href"
        :to="bottomCta.to"
        @click="handleBottomCta"
      >
        {{ bottomCta.text }}
      </Button>
    </div>
  </SectionContainer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import ProductCard from '../ui/ProductCard.vue'
import Button from '../ui/Button.vue'
import SectionContainer from '../ui/SectionContainer.vue'

interface ProductAction {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  icon?: string
  href?: string
  to?: string
}

interface Product {
  id?: string
  slug?: string
  name: string
  description?: string
  image?: string
  imageAlt?: string
  price?: number | string
  originalPrice?: number | string
  priceUnit?: string
  currency?: string
  features?: string[]
  badge?: string
  inStock?: boolean
  stockCount?: number
  rating?: number
  reviewCount?: number
  primaryAction?: ProductAction
  secondaryAction?: ProductAction
  category?: string
  order?: number
}

interface Category {
  slug: string
  name: string
}

interface BottomCta {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  icon?: string
  href?: string
  to?: string
}

interface Props {
  id?: string
  title?: string
  subtitle?: string
  products: Product[]
  categories?: Category[]
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  columns?: 1 | 2 | 3 | 4 | 5
  cardVariant?: 'default' | 'elevated' | 'outlined' | 'ghost'
  cardSize?: 'sm' | 'md' | 'lg'
  currency?: string
  showStock?: boolean
  showQuickActions?: boolean
  compareEnabled?: boolean
  clickableCards?: boolean
  defaultPrimaryAction?: ProductAction
  defaultSecondaryAction?: ProductAction
  showLoadMore?: boolean
  loadMoreCount?: number
  bottomCta?: BottomCta
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  columns: 3,
  cardVariant: 'elevated',
  cardSize: 'md',
  currency: '$',
  showStock: false,
  showQuickActions: false,
  compareEnabled: false,
  clickableCards: false,
  showLoadMore: false,
  loadMoreCount: 6
})

const emit = defineEmits<{
  productClick: [product: Product]
  productAction: [product: Product, actionType: 'primary' | 'secondary']
  wishlist: [product: Product]
  share: [product: Product]
  compare: [product: Product]
  loadMore: []
  bottomCta: []
}>()

// Reactive state
const selectedCategory = ref<string>('all')
const displayCount = ref(props.loadMoreCount)
const loadingMore = ref(false)

// Computed properties
const gridClasses = computed(() => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5'
  }
  
  return `grid ${columnClasses[props.columns]} gap-6 lg:gap-8`
})

const sortedProducts = computed(() => {
  return [...props.products].sort((a, b) => (a.order || 0) - (b.order || 0))
})

const filteredProducts = computed(() => {
  let filtered = sortedProducts.value
  
  // Filter by category
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }
  
  // Limit display count for load more functionality
  if (props.showLoadMore) {
    filtered = filtered.slice(0, displayCount.value)
  }
  
  return filtered
})

const hasMoreProducts = computed(() => {
  if (!props.showLoadMore) return false
  
  let totalCount = sortedProducts.value.length
  if (selectedCategory.value !== 'all') {
    totalCount = sortedProducts.value.filter(product => product.category === selectedCategory.value).length
  }
  
  return displayCount.value < totalCount
})

// Methods
const selectCategory = (categorySlug: string) => {
  selectedCategory.value = categorySlug
  displayCount.value = props.loadMoreCount // Reset display count when changing category
}

const loadMore = async () => {
  loadingMore.value = true
  displayCount.value += props.loadMoreCount
  emit('loadMore')
  
  // Simulate loading delay
  setTimeout(() => {
    loadingMore.value = false
  }, 500)
}

// Event handlers
const handleProductClick = (product: Product) => {
  emit('productClick', product)
}

const handleProductAction = (product: Product, actionType: 'primary' | 'secondary') => {
  emit('productAction', product, actionType)
}

const handleWishlist = (product: Product) => {
  emit('wishlist', product)
}

const handleShare = (product: Product) => {
  emit('share', product)
}

const handleCompare = (product: Product) => {
  emit('compare', product)
}

const handleBottomCta = () => {
  emit('bottomCta')
}

// Initialize categories with 'all' option
const categories = computed(() => {
  if (!props.categories) return []
  
  return [
    { slug: 'all', name: 'All Products' },
    ...props.categories
  ]
})
</script>
