<template>
  <div :class="cardClasses">
    <div v-if="imageUrl || $slots.image" class="relative overflow-hidden">
      <div v-if="$slots.image" class="w-full">
        <slot name="image" />
      </div>
      <NuxtImg
        v-else-if="imageUrl"
        :src="imageUrl"
        :alt="imageAlt"
        :class="imageClasses"
        loading="lazy"
      />
      <div v-if="badge" class="absolute top-4 left-4">
        <span class="bg-primary text-secondary px-3 py-1 rounded-full text-sm font-medium">
          {{ badge }}
        </span>
      </div>
    </div>
    
    <div :class="contentClasses">
      <div v-if="title || $slots.title" class="mb-3">
        <slot name="title">
          <h3 :class="titleClasses">{{ title }}</h3>
        </slot>
      </div>
      
      <div v-if="description || $slots.description" class="mb-4">
        <slot name="description">
          <p class="text-primary/80 leading-relaxed">{{ description }}</p>
        </slot>
      </div>
      
      <div v-if="$slots.content" class="mb-4">
        <slot name="content" />
      </div>
      
      <div v-if="price" class="mb-4">
        <span class="text-2xl font-bold text-primary">{{ price }}</span>
        <span v-if="priceUnit" class="text-primary/60 ml-1">{{ priceUnit }}</span>
      </div>
      
      <div v-if="$slots.actions" class="mt-auto">
        <slot name="actions" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  imageUrl?: string
  imageAlt?: string
  title?: string
  description?: string
  price?: string
  priceUnit?: string
  badge?: string
  hover?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  imageAlt: '',
  hover: true,
  clickable: false
})

const baseClasses = 'rounded-xl overflow-hidden transition-all duration-300'

const variantClasses = computed(() => {
  const variants = {
    default: 'bg-secondary/50 backdrop-blur-sm',
    elevated: 'bg-secondary shadow-lg hover:shadow-xl',
    outlined: 'bg-secondary/30 border border-primary/20',
    ghost: 'bg-transparent'
  }
  return variants[props.variant]
})

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  return sizes[props.size]
})

const cardClasses = computed(() => {
  return [
    baseClasses,
    variantClasses.value,
    sizeClasses.value,
    props.hover ? 'hover:scale-105' : '',
    props.clickable ? 'cursor-pointer' : '',
    'flex flex-col h-full'
  ].filter(Boolean).join(' ')
})

const imageClasses = computed(() => {
  const sizes = {
    sm: 'h-32',
    md: 'h-48',
    lg: 'h-64'
  }
  return `w-full object-cover ${sizes[props.size]}`
})

const contentClasses = computed(() => {
  const padding = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  return `${padding[props.size]} flex flex-col flex-1`
})

const titleClasses = computed(() => {
  const sizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }
  return `font-bold text-primary ${sizes[props.size]}`
})
</script>
