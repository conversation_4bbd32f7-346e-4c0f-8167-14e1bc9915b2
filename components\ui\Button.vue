<template>
  <component
    :is="tag"
    :href="href"
    :to="to"
    :target="target"
    :rel="rel"
    :class="buttonClasses"
    :disabled="disabled"
    @click="handleClick"
  >
    <Icon v-if="icon && !iconRight" :name="icon" :class="iconClasses" />
    <span v-if="$slots.default"><slot /></span>
    <Icon v-if="icon && iconRight" :name="icon" :class="iconClasses" />
  </component>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  href?: string
  to?: string
  target?: string
  rel?: string
  disabled?: boolean
  loading?: boolean
  icon?: string
  iconRight?: boolean
  fullWidth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  target: '_self',
  disabled: false,
  loading: false,
  iconRight: false,
  fullWidth: false
})

const emit = defineEmits<{
  click: [event: Event]
}>()

const tag = computed(() => {
  if (props.href) return 'a'
  if (props.to) return 'NuxtLink'
  return 'button'
})

const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'

const variantClasses = computed(() => {
  const variants = {
    primary: 'bg-primary text-secondary hover:bg-primary/90 focus:ring-primary/50 shadow-lg hover:shadow-xl',
    secondary: 'bg-secondary text-primary hover:bg-secondary/90 focus:ring-secondary/50 border border-primary/20',
    outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-secondary focus:ring-primary/50',
    ghost: 'text-primary hover:bg-primary/10 focus:ring-primary/50',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50'
  }
  return variants[props.variant]
})

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',
    md: 'px-4 py-2 text-base rounded-lg gap-2',
    lg: 'px-6 py-3 text-lg rounded-lg gap-2.5',
    xl: 'px-8 py-4 text-xl rounded-xl gap-3'
  }
  return sizes[props.size]
})

const buttonClasses = computed(() => {
  return [
    baseClasses,
    variantClasses.value,
    sizeClasses.value,
    props.fullWidth ? 'w-full' : '',
    props.loading ? 'cursor-wait' : ''
  ].filter(Boolean).join(' ')
})

const iconClasses = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7'
  }
  return sizes[props.size]
})

const handleClick = (event: Event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
